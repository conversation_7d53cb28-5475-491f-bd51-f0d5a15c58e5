// components/CustomVisualizer.tsx
import { Text } from 'moti';
import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import tw from 'twrnc';

type Props = {
  track: any;
  barCount?: number;
  color?: string;
};

export default function CustomVisualizer({ track, barCount = 5, color = '#00f' }: Props) {
  const [level, setLevel] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      if (track?.audioLevel !== undefined) {
        setLevel(track.audioLevel);
      }
    }, 100); // check every 100ms

    return () => clearInterval(interval);
  }, [track]);

  const activeBars = Math.round(level * barCount);

  return (
    <View style={tw`flex-row items-end h-10 gap-1`}>
      {Array.from({ length: barCount }).map((_, idx) => (
        <View
          key={idx}
          style={[
            tw`w-2 rounded`,
            {
              height: (idx + 1) * 6,
              backgroundColor: idx < activeBars ? color : '#ccc',
            },
          ]}
        />
      ))}

      <Text>{level}</Text>
    </View>
  );
}
