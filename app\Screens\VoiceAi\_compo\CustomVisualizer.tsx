// components/CustomVisualizer.tsx
import { Text, View as MotiView } from 'moti';
import React, { useEffect, useState, useRef } from 'react';
import { View, Animated } from 'react-native';
import tw from 'twrnc';
import { Track } from 'livekit-client';

type Props = {
  track: Track | undefined;
  barCount?: number;
  color?: string;
};

export default function CustomVisualizer({ track, barCount = 5, color = '#00f' }: Props) {
  const [level, setLevel] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();
  const animationRef = useRef<number>();

  useEffect(() => {
    if (!track) {
      setLevel(0);
      return;
    }

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    let audioContext: AudioContext | null = null;
    let analyser: AnalyserNode | null = null;
    let dataArray: Uint8Array | null = null;
    let source: MediaStreamAudioSourceNode | null = null;

    // Set up real audio level monitoring using Web Audio API
    const setupAudioAnalysis = async () => {
      try {
        if (track && track.mediaStreamTrack && track.mediaStreamTrack.readyState === 'live') {
          // Create audio context
          audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

          // Create analyser node
          analyser = audioContext.createAnalyser();
          analyser.fftSize = 256;
          analyser.smoothingTimeConstant = 0.8;

          // Create data array for frequency data
          const bufferLength = analyser.frequencyBinCount;
          dataArray = new Uint8Array(bufferLength);

          // Create media stream source
          const stream = new MediaStream([track.mediaStreamTrack]);
          source = audioContext.createMediaStreamSource(stream);
          source.connect(analyser);

          return true;
        }
        return false;
      } catch (error) {
        console.warn('Error setting up audio analysis:', error);
        return false;
      }
    };

    const updateLevel = () => {
      try {
        if (analyser && dataArray && track && track.mediaStreamTrack && track.mediaStreamTrack.readyState === 'live') {
          // Check if track is active and not muted
          const isActive = track.mediaStreamTrack.enabled && !track.isMuted;

          if (isActive) {
            // Get frequency data
            analyser.getByteFrequencyData(dataArray);

            // Calculate average volume
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
              sum += dataArray[i];
            }
            const average = sum / dataArray.length;

            // Normalize to 0-1 range and apply some smoothing
            const normalizedLevel = Math.min(1, average / 128);
            setLevel(normalizedLevel);
          } else {
            setLevel(0);
          }
        } else {
          // Fallback to simulation if Web Audio API fails
          const isActive = track && track.mediaStreamTrack &&
                           track.mediaStreamTrack.enabled &&
                           !track.isMuted &&
                           track.mediaStreamTrack.readyState === 'live';

          if (isActive) {
            const baseLevel = 0.2;
            const variation = Math.random() * 0.7;
            const newLevel = Math.min(1, baseLevel + variation);
            setLevel(newLevel);
          } else {
            setLevel(0);
          }
        }
      } catch (error) {
        console.warn('Error updating audio level:', error);
        setLevel(0);
      }
    };

    // Initialize audio analysis
    setupAudioAnalysis().then((success) => {
      if (success) {
        console.log('Audio analysis setup successful');
      } else {
        console.log('Falling back to simulation mode');
      }

      // Start monitoring
      intervalRef.current = setInterval(updateLevel, 100);
      updateLevel(); // Initial call
    });

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }

      // Clean up Web Audio API resources
      try {
        if (source) {
          source.disconnect();
        }
        if (audioContext && audioContext.state !== 'closed') {
          audioContext.close();
        }
      } catch (error) {
        console.warn('Error cleaning up audio context:', error);
      }
    };
  }, [track]);

  const activeBars = Math.round(level * barCount);

  return (
    <View style={tw`flex-row items-end h-16 gap-1 justify-center`}>
      {Array.from({ length: barCount }).map((_, idx) => {
        const isActive = idx < activeBars;
        const baseHeight = 20;
        const maxHeight = 50;
        const height = baseHeight + (idx + 1) * ((maxHeight - baseHeight) / barCount);

        return (
          <View
            key={idx}
            style={[
              tw`w-3 rounded-full`,
              {
                height: isActive ? height : baseHeight,
                backgroundColor: isActive ? color : '#e5e5e5',
                opacity: isActive ? 1 : 0.4,
              },
            ]}
          />
        );
      })}

      {/* Debug info - remove in production */}
      {__DEV__ && (
        <Text style={tw`text-xs ml-2 text-gray-600`}>
          {level.toFixed(2)}
        </Text>
      )}
    </View>
  );
}
